import { getDashboardWidgetsByType } from '../services/dashboard.service';

describe('Dashboard Service', () => {
    const mockOrganizationId = 'test-org-123';
    const mockUserId = 1;
    const mockFilters = {};

    describe('getDashboardWidgetsByType', () => {
        it('should return user dashboard widgets for user type', async () => {
            const result = await getDashboardWidgetsByType('user', mockOrganizationId, mockUserId, mockFilters);

            expect(result).toBeDefined();
            expect(result.dashboardType).toBe('user');
            expect(result.widgets).toBeInstanceOf(Array);
            expect(result.totalWidgets).toBeGreaterThan(0);
            
            // Check for user-specific widgets
            const widgetTitles = result.widgets.map(w => w.title);
            expect(widgetTitles).toContain('My Tasks');
            expect(widgetTitles).toContain('My Leave Balance');
            expect(widgetTitles).toContain('My Recent Activities');
            expect(widgetTitles).toContain('My Performance');
        });

        it('should return admin dashboard widgets for admin type', async () => {
            const result = await getDashboardWidgetsByType('admin', mockOrganizationId, mockUserId, mockFilters);

            expect(result).toBeDefined();
            expect(result.dashboardType).toBe('admin');
            expect(result.widgets).toBeInstanceOf(Array);
            expect(result.totalWidgets).toBeGreaterThan(0);
            
            // Check for admin-specific widgets
            const widgetTitles = result.widgets.map(w => w.title);
            expect(widgetTitles).toContain('Organization Overview');
            expect(widgetTitles).toContain('Pending Approvals');
            expect(widgetTitles).toContain('Department Performance');
            expect(widgetTitles).toContain('System Health');
            expect(widgetTitles).toContain('Recent User Activities');
        });

        it('should return manager dashboard widgets for manager type', async () => {
            const result = await getDashboardWidgetsByType('manager', mockOrganizationId, mockUserId, mockFilters);

            expect(result).toBeDefined();
            expect(result.dashboardType).toBe('manager');
            expect(result.widgets).toBeInstanceOf(Array);
            expect(result.totalWidgets).toBeGreaterThan(0);
            
            // Check for manager-specific widgets
            const widgetTitles = result.widgets.map(w => w.title);
            expect(widgetTitles).toContain('Team Overview');
            expect(widgetTitles).toContain('Team Performance');
            expect(widgetTitles).toContain('Project Status');
            expect(widgetTitles).toContain('Team Requests');
        });

        it('should return super admin dashboard widgets for super_admin type', async () => {
            const result = await getDashboardWidgetsByType('super_admin', mockOrganizationId, mockUserId, mockFilters);

            expect(result).toBeDefined();
            expect(result.dashboardType).toBe('super_admin');
            expect(result.widgets).toBeInstanceOf(Array);
            expect(result.totalWidgets).toBeGreaterThan(0);
            
            // Check for super admin-specific widgets
            const widgetTitles = result.widgets.map(w => w.title);
            expect(widgetTitles).toContain('Global System Stats');
            expect(widgetTitles).toContain('Organization Management');
            expect(widgetTitles).toContain('System Resources');
        });

        it('should handle case insensitive dashboard types', async () => {
            const result = await getDashboardWidgetsByType('USER', mockOrganizationId, mockUserId, mockFilters);

            expect(result).toBeDefined();
            expect(result.dashboardType).toBe('USER');
            expect(result.widgets).toBeInstanceOf(Array);
            expect(result.totalWidgets).toBeGreaterThan(0);
        });

        it('should throw error for unsupported dashboard type', async () => {
            await expect(
                getDashboardWidgetsByType('invalid_type', mockOrganizationId, mockUserId, mockFilters)
            ).rejects.toThrow('Unsupported dashboard type: invalid_type');
        });

        it('should return widgets with correct structure', async () => {
            const result = await getDashboardWidgetsByType('user', mockOrganizationId, mockUserId, mockFilters);

            expect(result.widgets.length).toBeGreaterThan(0);
            
            // Check widget structure
            result.widgets.forEach(widget => {
                expect(widget).toHaveProperty('id');
                expect(widget).toHaveProperty('title');
                expect(widget).toHaveProperty('type');
                expect(widget).toHaveProperty('data');
                expect(widget).toHaveProperty('order');
                
                expect(typeof widget.id).toBe('number');
                expect(typeof widget.title).toBe('string');
                expect(typeof widget.type).toBe('string');
                expect(typeof widget.data).toBe('object');
                expect(typeof widget.order).toBe('number');
            });
        });

        it('should return widgets in correct order', async () => {
            const result = await getDashboardWidgetsByType('user', mockOrganizationId, mockUserId, mockFilters);

            // Check if widgets are ordered correctly
            for (let i = 1; i < result.widgets.length; i++) {
                expect(result.widgets[i].order).toBeGreaterThanOrEqual(result.widgets[i - 1].order);
            }
        });

        it('should return correct total widget count', async () => {
            const result = await getDashboardWidgetsByType('admin', mockOrganizationId, mockUserId, mockFilters);

            expect(result.totalWidgets).toBe(result.widgets.length);
        });
    });
});
