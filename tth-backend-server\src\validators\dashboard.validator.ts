import { Segments, Jo<PERSON>, celebrate } from "celebrate";
export default {
  addDashboard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dashboard_name: Joi.string().required(),
        dashboard_filter: Joi.string().required(),
        model_list: Joi.array().items(Joi.object()).unique().min(1).required()
      }),
    }),
  updateDashboard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dashboard_name: Joi.string().required(),
        dashboard_filter: Joi.string().required(),
        model_list: Joi.array().items(Joi.object()).unique().min(1).required()
      }),
    }),
};
