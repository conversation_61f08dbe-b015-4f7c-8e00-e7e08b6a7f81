import { Segments, Joi, celebrate } from "celebrate";
export default {
  addDashboard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dashboard_name: Joi.string().required(),
        dashboard_filter: Joi.string().required(),
        model_list: Joi.array().items(Joi.object()).unique().min(1).required()
      }),
    }),
  updateDashboard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dashboard_name: Joi.string().required(),
        dashboard_filter: Joi.string().required(),
        model_list: Joi.array().items(Joi.object()).unique().min(1).required()
      }),
    }),
  getDashboardWidgets: () =>
    celebrate({
      [Segments.QUERY]: Joi.object().keys({
        dashboardType: Joi.string()
          .valid('user', 'admin', 'manager', 'super_admin')
          .required()
          .messages({
            'any.only': 'Dashboard type must be one of: user, admin, manager, super_admin',
            'any.required': 'Dashboard type is required'
          }),
        // Optional filters
        branch_id: Joi.string().optional(),
        date_filter: Joi.string().optional(),
        filter_time_period: Joi.string().optional()
      }),
    }),
};
