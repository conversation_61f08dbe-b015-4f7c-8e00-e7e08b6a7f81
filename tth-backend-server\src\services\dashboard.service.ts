import { Dashboard, dashboard_status } from "../models/Dashboard";
import { DashboardModel, model_status } from "../models/DashboardModel";
import { User } from "../models/User";
import { generateDashboard } from "../helper/common";

/**
 * Dashboard Service
 * Contains business logic for retrieving dashboard widgets based on dashboard type
 */

export interface DashboardWidget {
    id: number;
    title: string;
    type: string;
    data: any;
    order: number;
}

export interface DashboardWidgetsResponse {
    dashboardType: string;
    widgets: DashboardWidget[];
    totalWidgets: number;
}

/**
 * Get dashboard widgets based on dashboard type
 * @param dashboardType - Type of dashboard (user, admin, manager)
 * @param organizationId - Organization ID
 * @param userId - User ID
 * @param filters - Optional filters for dashboard data
 * @returns Dashboard widgets specific to the dashboard type
 */
export const getDashboardWidgetsByType = async (
    dashboardType: string,
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidgetsResponse> => {
    try {
        let widgets: DashboardWidget[] = [];

        // Switch case to determine which widgets to return based on dashboard type
        switch (dashboardType.toLowerCase()) {
            case 'user':
                widgets = await getUserDashboardWidgets(organizationId, userId, filters);
                break;
            case 'admin':
                widgets = await getAdminDashboardWidgets(organizationId, userId, filters);
                break;
            case 'manager':
                widgets = await getManagerDashboardWidgets(organizationId, userId, filters);
                break;
            case 'super_admin':
                widgets = await getSuperAdminDashboardWidgets(organizationId, userId, filters);
                break;
            default:
                throw new Error(`Unsupported dashboard type: ${dashboardType}`);
        }

        return {
            dashboardType,
            widgets,
            totalWidgets: widgets.length
        };
    } catch (error) {
        console.error('Error in getDashboardWidgetsByType:', error);
        throw error;
    }
};

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users
 */
const getUserDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // User-specific widgets
        widgets.push({
            id: 1,
            title: "My Tasks",
            type: "task_summary",
            data: {
                pending: 5,
                completed: 12,
                overdue: 2
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "My Leave Balance",
            type: "leave_balance",
            data: {
                available: 15,
                used: 10,
                pending: 2
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "My Recent Activities",
            type: "activity_feed",
            data: {
                activities: [
                    { action: "DSR Submitted", date: "2024-01-15", status: "completed" },
                    { action: "Leave Request", date: "2024-01-14", status: "pending" }
                ]
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "My Performance",
            type: "performance_chart",
            data: {
                currentMonth: 85,
                lastMonth: 78,
                trend: "up"
            },
            order: 4
        });

        return widgets;
    } catch (error) {
        console.error('Error in getUserDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
const getAdminDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // Admin-specific widgets
        widgets.push({
            id: 1,
            title: "Organization Overview",
            type: "org_stats",
            data: {
                totalEmployees: 150,
                activeProjects: 25,
                pendingRequests: 8
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "Pending Approvals",
            type: "approval_queue",
            data: {
                leaveRequests: 5,
                expenseRequests: 3,
                dsrRequests: 12
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "Department Performance",
            type: "department_chart",
            data: {
                departments: [
                    { name: "IT", performance: 92 },
                    { name: "HR", performance: 88 },
                    { name: "Finance", performance: 95 }
                ]
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "System Health",
            type: "system_status",
            data: {
                uptime: "99.9%",
                activeUsers: 145,
                systemLoad: "Normal"
            },
            order: 4
        });

        widgets.push({
            id: 5,
            title: "Recent User Activities",
            type: "user_activity_log",
            data: {
                recentLogins: 25,
                newRegistrations: 3,
                lastActivity: "2 minutes ago"
            },
            order: 5
        });

        return widgets;
    } catch (error) {
        console.error('Error in getAdminDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get manager-specific dashboard widgets
 * Returns widgets relevant to managers
 */
const getManagerDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // Manager-specific widgets
        widgets.push({
            id: 1,
            title: "Team Overview",
            type: "team_stats",
            data: {
                teamSize: 12,
                activeMembers: 11,
                onLeave: 1
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "Team Performance",
            type: "team_performance",
            data: {
                averagePerformance: 87,
                topPerformer: "John Doe",
                improvementNeeded: 2
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "Project Status",
            type: "project_overview",
            data: {
                activeProjects: 5,
                completedThisMonth: 3,
                overdue: 1
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "Team Requests",
            type: "team_requests",
            data: {
                pendingLeave: 2,
                pendingExpenses: 4,
                pendingDSR: 6
            },
            order: 4
        });

        return widgets;
    } catch (error) {
        console.error('Error in getManagerDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get super admin-specific dashboard widgets
 * Returns widgets relevant to super administrators
 */
const getSuperAdminDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // Super Admin-specific widgets
        widgets.push({
            id: 1,
            title: "Global System Stats",
            type: "global_stats",
            data: {
                totalOrganizations: 50,
                totalUsers: 2500,
                systemUptime: "99.99%"
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "Organization Management",
            type: "org_management",
            data: {
                activeOrgs: 48,
                pendingSetup: 2,
                recentlyAdded: 3
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "System Resources",
            type: "resource_monitor",
            data: {
                cpuUsage: "45%",
                memoryUsage: "62%",
                diskUsage: "78%"
            },
            order: 3
        });

        return widgets;
    } catch (error) {
        console.error('Error in getSuperAdminDashboardWidgets:', error);
        throw error;
    }
};

export default {
    getDashboardWidgetsByType,
    getUserDashboardWidgets,
    getAdminDashboardWidgets,
    getManagerDashboardWidgets,
    getSuperAdminDashboardWidgets
};
